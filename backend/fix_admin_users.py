#!/usr/bin/env python3
"""
Fix admin users fetching issue by ensuring database schema is correct
"""

import sqlite3
import os
import sys
from datetime import datetime

def fix_database():
    """Fix database schema and ensure users can be fetched"""
    
    # Database path
    db_path = 'instance/coachcentral.db'
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found!")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Fixing database schema...")
        
        # Check if last_login column exists in users table
        cursor.execute("PRAGMA table_info(users)")
        users_columns = [column[1] for column in cursor.fetchall()]
        
        if 'last_login' not in users_columns:
            print("Adding last_login column to users table...")
            cursor.execute("ALTER TABLE users ADD COLUMN last_login DATETIME")
            print("✅ Added last_login column to users table")
        else:
            print("ℹ️  last_login column already exists in users table")
        
        # Check if last_login column exists in approved_users table
        try:
            cursor.execute("PRAGMA table_info(approved_users)")
            approved_users_columns = [column[1] for column in cursor.fetchall()]
            
            if 'last_login' not in approved_users_columns:
                print("Adding last_login column to approved_users table...")
                cursor.execute("ALTER TABLE approved_users ADD COLUMN last_login DATETIME")
                print("✅ Added last_login column to approved_users table")
            else:
                print("ℹ️  last_login column already exists in approved_users table")
        except sqlite3.OperationalError:
            print("ℹ️  approved_users table doesn't exist (this is normal)")
        
        # Commit changes
        conn.commit()
        
        # Show current users
        print("\n📋 Current users in database:")
        cursor.execute("SELECT id, name, email, status FROM users")
        users = cursor.fetchall()
        
        if users:
            for user in users:
                print(f"  - ID: {user[0]}, Name: {user[1]}, Email: {user[2]}, Status: {user[3]}")
        else:
            print("  No users found")
        
        print(f"\n✅ Database fix completed! Found {len(users)} users.")
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    print("🚀 Starting database fix...")
    success = fix_database()
    
    if success:
        print("\n🎉 Database fix completed successfully!")
        print("\n📋 Next steps:")
        print("1. Restart your backend server")
        print("2. Try accessing the admin panel again")
        print("\n🔑 Test Credentials:")
        print("   Admin: <EMAIL> / admin123")
        print("   User 1: <EMAIL> / password123")
        print("   User 2: <EMAIL> / vivek123")
    else:
        print("\n❌ Database fix failed!")
