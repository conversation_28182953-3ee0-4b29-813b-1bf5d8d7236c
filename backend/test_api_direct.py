#!/usr/bin/env python3
"""
Test API directly using Flask test client
"""

import sys
import os
sys.path.append('.')

from app import create_app
from models import db, Admin, User

def test_api_directly():
    """Test the API endpoints directly"""
    
    app = create_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("🧪 Testing API endpoints directly...")
            
            # Test 1: Admin login
            print("\n🔐 Testing admin login...")
            login_response = client.post('/api/admin/login', 
                json={'email': '<EMAIL>', 'password': 'admin123'})
            
            print(f"Login status: {login_response.status_code}")
            if login_response.status_code == 200:
                print("✅ Admin login successful")
                login_data = login_response.get_json()
                print(f"Admin: {login_data.get('admin', {}).get('name', 'Unknown')}")
            else:
                print(f"❌ Admin login failed: {login_response.get_data(as_text=True)}")
                return
            
            # Test 2: Get approved users
            print("\n👥 Testing get approved users...")
            try:
                # Direct database query first
                approved_users = User.query.filter_by(status='approved').all()
                print(f"Direct DB query found {len(approved_users)} approved users:")
                for user in approved_users:
                    print(f"  - {user.name} ({user.email})")
                
                # Now test the API endpoint
                users_response = client.get('/api/admin/approved-users')
                print(f"API response status: {users_response.status_code}")
                
                if users_response.status_code == 200:
                    users_data = users_response.get_json()
                    users_list = users_data.get('approved_users', [])
                    print(f"✅ API returned {len(users_list)} approved users")
                    
                    for user in users_list:
                        print(f"  - {user.get('name', 'Unknown')} ({user.get('email', 'Unknown')})")
                        print(f"    Status: {user.get('status', 'Unknown')}")
                        print(f"    Last Login: {user.get('last_login', 'Never')}")
                else:
                    print(f"❌ API failed: {users_response.get_data(as_text=True)}")
                    
            except Exception as e:
                print(f"❌ Error during API test: {e}")
                import traceback
                traceback.print_exc()
            
            # Test 3: Get pending users
            print("\n⏳ Testing get pending users...")
            try:
                pending_response = client.get('/api/admin/pending-users')
                print(f"Pending users API status: {pending_response.status_code}")
                
                if pending_response.status_code == 200:
                    pending_data = pending_response.get_json()
                    pending_list = pending_data.get('pending_users', [])
                    print(f"✅ API returned {len(pending_list)} pending users")
                else:
                    print(f"❌ Pending users API failed: {pending_response.get_data(as_text=True)}")
                    
            except Exception as e:
                print(f"❌ Error during pending users test: {e}")

if __name__ == '__main__':
    test_api_directly()
