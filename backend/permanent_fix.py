#!/usr/bin/env python3
"""
Permanent fix for database compatibility issues
This script will:
1. Clean up all database files
2. Create a fresh database with correct schema
3. Add proper migration handling
4. Set up the application correctly
"""

import os
import sys
import sqlite3
import shutil
from datetime import datetime

# Add current directory to path
sys.path.append('.')

def cleanup_old_files():
    """Remove all old database files and cache"""
    print("🧹 Cleaning up old files...")
    
    # Remove all database files
    db_patterns = [
        'instance/coachcentral.db',
        'coachcentral.db', 
        'coaching_app.db',
        '*.db'
    ]
    
    for pattern in db_patterns:
        if '*' in pattern:
            import glob
            for file in glob.glob(pattern):
                if os.path.exists(file):
                    os.remove(file)
                    print(f"🗑️  Removed {file}")
        else:
            if os.path.exists(pattern):
                os.remove(pattern)
                print(f"🗑️  Removed {pattern}")
    
    # Remove Python cache
    if os.path.exists('__pycache__'):
        shutil.rmtree('__pycache__')
        print("🗑️  Removed __pycache__")
    
    # Ensure instance directory exists
    os.makedirs('instance', exist_ok=True)
    print("📁 Created instance directory")

def create_database_with_schema():
    """Create database with correct schema"""
    print("📋 Creating database with correct schema...")
    
    db_path = 'instance/coachcentral.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create all tables with correct schema
    tables = {
        'admins': '''
            CREATE TABLE admins (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(120) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''',
        'users': '''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(120) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                phone VARCHAR(20) NOT NULL,
                credential VARCHAR(50) NOT NULL,
                institution VARCHAR(200) NOT NULL,
                country VARCHAR(100) NOT NULL,
                resume_filename VARCHAR(255),
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                approved_at DATETIME,
                declined_at DATETIME,
                last_login DATETIME
            )
        ''',
        'pending_users': '''
            CREATE TABLE pending_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(120) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                phone VARCHAR(20) NOT NULL,
                credential VARCHAR(50) NOT NULL,
                institution VARCHAR(200) NOT NULL,
                country VARCHAR(100) NOT NULL,
                resume_filename VARCHAR(255),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''',
        'approved_users': '''
            CREATE TABLE approved_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(120) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                phone VARCHAR(20) NOT NULL,
                credential VARCHAR(50) NOT NULL,
                institution VARCHAR(200) NOT NULL,
                country VARCHAR(100) NOT NULL,
                resume_filename VARCHAR(255),
                approved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME NOT NULL,
                last_login DATETIME
            )
        ''',
        'availability_slots': '''
            CREATE TABLE availability_slots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                available_time_from DATETIME NOT NULL,
                available_time_to DATETIME NOT NULL,
                status VARCHAR(20) DEFAULT 'available',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''',
        'bookings': '''
            CREATE TABLE bookings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                coach_id INTEGER NOT NULL,
                booking_time_from DATETIME NOT NULL,
                booking_time_to DATETIME NOT NULL,
                status VARCHAR(20) DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                approved_at DATETIME,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (coach_id) REFERENCES users (id)
            )
        '''
    }
    
    for table_name, sql in tables.items():
        cursor.execute(sql)
        print(f"✅ Created {table_name} table")
    
    conn.commit()
    conn.close()
    print("✅ Database schema created successfully")

def add_test_data():
    """Add admin and test users"""
    print("👥 Adding test data...")
    
    db_path = 'instance/coachcentral.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Import bcrypt for password hashing
    import bcrypt
    
    # Add admin
    admin_password = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    cursor.execute('''
        INSERT INTO admins (name, email, password_hash)
        VALUES (?, ?, ?)
    ''', ('System Admin', '<EMAIL>', admin_password))
    print("✅ Added admin user")
    
    # Add test users
    test_users = [
        ('John Doe', '<EMAIL>', 'password123', '+1-234-567-8900', 'ACC In Progress', 'Coaching Academy', 'USA'),
        ('Vivek Agarwal', '<EMAIL>', 'vivek123', '+91-9876543210', 'PCC Certified', 'International Coach Federation', 'India'),
        ('Sarah Johnson', '<EMAIL>', 'sarah123', '******-123-4567', 'MCC Certified', 'Global Coaching Institute', 'Canada'),
        ('Test Pending', '<EMAIL>', 'pending123', '******-999-0000', 'ACC In Progress', 'Test Institute', 'USA')
    ]
    
    for i, user_data in enumerate(test_users):
        name, email, password, phone, credential, institution, country = user_data
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        # Make last user pending, others approved
        status = 'pending' if i == len(test_users) - 1 else 'approved'
        approved_at = None if status == 'pending' else datetime.utcnow()
        
        cursor.execute('''
            INSERT INTO users (name, email, password_hash, phone, credential, institution, country, status, approved_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, email, password_hash, phone, credential, institution, country, status, approved_at))
        
        print(f"✅ Added user: {name} ({status})")
    
    conn.commit()
    conn.close()
    print("✅ Test data added successfully")

def create_startup_script():
    """Create a reliable startup script"""
    print("📝 Creating startup script...")
    
    startup_content = '''#!/usr/bin/env python3
"""
Reliable startup script for CoachCentral
"""

import os
import sys
from datetime import datetime

# Ensure we're in the right directory
os.chdir(os.path.dirname(os.path.abspath(__file__)))

# Add to Python path
sys.path.insert(0, '.')

def start_server():
    """Start the server with proper initialization"""
    try:
        from app import create_app
        from models import db
        
        print("🚀 Starting CoachCentral Server...")
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        app = create_app()
        
        # Verify database connection
        with app.app_context():
            try:
                # Test database connection
                from models import Admin, User
                admin_count = Admin.query.count()
                user_count = User.query.count()
                
                print(f"📊 Database connected: {admin_count} admins, {user_count} users")
                
            except Exception as e:
                print(f"⚠️  Database issue: {e}")
                print("🔧 Run: python3 permanent_fix.py to fix")
                return
        
        print("\\n🔑 Login Credentials:")
        print("   Admin: <EMAIL> / admin123")
        print("   User 1: <EMAIL> / password123")
        print("   User 2: <EMAIL> / vivek123")
        print("   User 3: <EMAIL> / sarah123")
        print("   Pending: <EMAIL> / pending123")
        print("\\n🌐 Server URL: http://127.0.0.1:8000")
        print("=" * 50)
        
        # Start server
        app.run(debug=True, host='127.0.0.1', port=8000)
        
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    start_server()
'''
    
    with open('start_server_reliable.py', 'w') as f:
        f.write(startup_content)
    
    # Make it executable
    os.chmod('start_server_reliable.py', 0o755)
    print("✅ Created start_server_reliable.py")

def main():
    """Main fix function"""
    print("🔧 PERMANENT FIX FOR COACHCENTRAL DATABASE ISSUES")
    print("=" * 60)
    
    try:
        # Step 1: Cleanup
        cleanup_old_files()
        
        # Step 2: Create database
        create_database_with_schema()
        
        # Step 3: Add test data
        add_test_data()
        
        # Step 4: Create startup script
        create_startup_script()
        
        print("\n🎉 PERMANENT FIX COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("✅ Database schema fixed")
        print("✅ Test data added")
        print("✅ Reliable startup script created")
        
        print("\n📋 TO START THE APPLICATION:")
        print("   python3 start_server_reliable.py")
        
        print("\n🔑 LOGIN CREDENTIALS:")
        print("   Admin: <EMAIL> / admin123")
        print("   User 1: <EMAIL> / password123")
        print("   User 2: <EMAIL> / vivek123")
        print("   User 3: <EMAIL> / sarah123")
        print("   Pending: <EMAIL> / pending123")
        
        return True
        
    except Exception as e:
        print(f"\n❌ PERMANENT FIX FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
