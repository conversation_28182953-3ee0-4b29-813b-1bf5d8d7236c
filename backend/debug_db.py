#!/usr/bin/env python3
"""
Debug database issues
"""

import sqlite3
import os

def debug_database():
    """Debug the database to see what's wrong"""
    
    db_path = 'instance/coachcentral.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} not found!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Database Debug Information")
        print("=" * 50)
        
        # Check tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📋 Tables in database: {[table[0] for table in tables]}")
        
        # Check users table structure
        if ('users',) in tables:
            print("\n👥 Users table structure:")
            cursor.execute("PRAGMA table_info(users)")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
            
            # Check users data
            cursor.execute("SELECT id, name, email, status FROM users")
            users = cursor.fetchall()
            print(f"\n👥 Users in database ({len(users)} total):")
            for user in users:
                print(f"  - ID: {user[0]}, Name: {user[1]}, Email: {user[2]}, Status: {user[3]}")
        
        # Check approved_users table if it exists
        if ('approved_users',) in tables:
            print("\n✅ Approved_users table structure:")
            cursor.execute("PRAGMA table_info(approved_users)")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
            
            cursor.execute("SELECT id, name, email FROM approved_users")
            approved_users = cursor.fetchall()
            print(f"\n✅ Approved users in database ({len(approved_users)} total):")
            for user in approved_users:
                print(f"  - ID: {user[0]}, Name: {user[1]}, Email: {user[2]}")
        
        # Check admins table
        if ('admins',) in tables:
            print("\n🔐 Admins table structure:")
            cursor.execute("PRAGMA table_info(admins)")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
            
            cursor.execute("SELECT id, name, email FROM admins")
            admins = cursor.fetchall()
            print(f"\n🔐 Admins in database ({len(admins)} total):")
            for admin in admins:
                print(f"  - ID: {admin[0]}, Name: {admin[1]}, Email: {admin[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    debug_database()
