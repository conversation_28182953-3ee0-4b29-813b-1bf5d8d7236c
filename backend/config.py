import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Simplified Database Configuration - Use SQLite for consistency
    # This ensures we have one unified database without configuration complexity
    SQLALCHEMY_DATABASE_URI = 'sqlite:///instance/coachcentral.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Ensure instance directory exists
    @staticmethod
    def init_app(app):
        instance_dir = os.path.join(app.root_path, 'instance')
        if not os.path.exists(instance_dir):
            os.makedirs(instance_dir)

    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Flask Configuration
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'dev-secret-key-change-in-production')

    # Session Configuration for Cross-Origin Requests
    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = False  # Allow JavaScript access for debugging
    SESSION_COOKIE_SAMESITE = 'Lax'  # Use Lax for development (None requires HTTPS)
    SESSION_COOKIE_DOMAIN = None  # Allow cookies across subdomains
    PERMANENT_SESSION_LIFETIME = 3600  # 1 hour session lifetime
    
    # File Upload Configuration
    UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', 'uploads')
    MAX_CONTENT_LENGTH = int(os.getenv('MAX_CONTENT_LENGTH', 16777216))  # 16MB
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx'}
    
    @staticmethod
    def allowed_file(filename):
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS
