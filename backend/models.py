from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime, time
import bcrypt

db = SQLAlchemy()

class Admin(UserMixin, db.Model):
    """Admin users who can manage the system"""
    __tablename__ = 'admins'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    def check_password(self, password):
        """Check if provided password matches hash"""
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class User(UserMixin, db.Model):
    """Unified user model for all users (coaches and clients)"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    credential = db.Column(db.String(50), nullable=False)
    institution = db.Column(db.String(200), nullable=False)
    country = db.Column(db.String(100), nullable=False)
    resume_filename = db.Column(db.String(255), nullable=True)
    status = db.Column(db.String(20), default='pending', nullable=False, index=True)  # pending, approved, declined
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    approved_at = db.Column(db.DateTime, nullable=True)
    declined_at = db.Column(db.DateTime, nullable=True)
    last_login = db.Column(db.DateTime, nullable=True)

    # Constraints
    __table_args__ = (
        db.CheckConstraint("status IN ('pending', 'approved', 'declined')", name='check_user_status'),
    )

    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    def check_password(self, password):
        """Check if provided password matches hash"""
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))

    def approve(self):
        """Approve the user"""
        self.status = 'approved'
        self.approved_at = datetime.utcnow()
        self.declined_at = None

    def decline(self):
        """Decline the user"""
        self.status = 'declined'
        self.declined_at = datetime.utcnow()
        self.approved_at = None

    def is_approved(self):
        """Check if user is approved"""
        return self.status == 'approved'

    def is_pending(self):
        """Check if user is pending"""
        return self.status == 'pending'

    def is_declined(self):
        """Check if user is declined"""
        return self.status == 'declined'

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'credential': self.credential,
            'institution': self.institution,
            'country': self.country,
            'resume_filename': self.resume_filename,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'declined_at': self.declined_at.isoformat() if self.declined_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

# REMOVED: PendingUser and ApprovedUser models - consolidated into User model

class AvailabilitySlot(db.Model):
    """Model for storing specific date/time availability slots"""
    __tablename__ = 'availability_slots'

    id = db.Column(db.Integer, primary_key=True)
    coach_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    date = db.Column(db.Date, nullable=False, index=True)
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    status = db.Column(db.String(20), default='free', nullable=False, index=True)  # free, taken, blocked
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Constraints
    __table_args__ = (
        db.CheckConstraint("status IN ('free', 'taken', 'blocked')", name='check_availability_status'),
        db.CheckConstraint("start_time < end_time", name='check_time_order'),
        db.UniqueConstraint('coach_id', 'date', 'start_time', name='unique_coach_slot'),
    )

    # Relationship - only to approved users
    coach = db.relationship('User', backref='availability_slots',
                           primaryjoin="and_(AvailabilitySlot.coach_id==User.id, User.status=='approved')")

    def to_dict(self):
        return {
            'id': self.id,
            'coach_id': self.coach_id,
            'date': self.date.isoformat() if self.date else None,
            'start_time': self.start_time.strftime('%H:%M') if self.start_time else None,
            'end_time': self.end_time.strftime('%H:%M') if self.end_time else None,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'coach_name': self.coach.name if self.coach else None
        }


class Booking(db.Model):
    """Model for storing booking requests and approved bookings"""
    __tablename__ = 'bookings'

    id = db.Column(db.Integer, primary_key=True)
    coach_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    client_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    availability_slot_id = db.Column(db.Integer, db.ForeignKey('availability_slots.id', ondelete='SET NULL'), nullable=True)
    date = db.Column(db.Date, nullable=False, index=True)
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    status = db.Column(db.String(20), default='pending', nullable=False, index=True)  # pending, approved, rejected, cancelled
    session_type = db.Column(db.String(20), default='virtual', nullable=False)  # virtual, in-person
    specialization = db.Column(db.String(100), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    approved_at = db.Column(db.DateTime, nullable=True)
    rejected_at = db.Column(db.DateTime, nullable=True)
    cancelled_at = db.Column(db.DateTime, nullable=True)

    # Constraints
    __table_args__ = (
        db.CheckConstraint("status IN ('pending', 'approved', 'rejected', 'cancelled')", name='check_booking_status'),
        db.CheckConstraint("session_type IN ('virtual', 'in-person')", name='check_session_type'),
        db.CheckConstraint("start_time < end_time", name='check_booking_time_order'),
        db.CheckConstraint("coach_id != client_id", name='check_no_self_booking'),
    )

    # Relationships - only to approved users
    coach = db.relationship('User', foreign_keys=[coach_id], backref='coach_bookings',
                           primaryjoin="and_(Booking.coach_id==User.id, User.status=='approved')")
    client = db.relationship('User', foreign_keys=[client_id], backref='client_bookings',
                            primaryjoin="and_(Booking.client_id==User.id, User.status=='approved')")
    availability_slot = db.relationship('AvailabilitySlot', backref='bookings')

    def to_dict(self):
        return {
            'id': self.id,
            'coach_id': self.coach_id,
            'client_id': self.client_id,
            'availability_slot_id': self.availability_slot_id,
            'date': self.date.isoformat() if self.date else None,
            'start_time': self.start_time.strftime('%H:%M') if self.start_time else None,
            'end_time': self.end_time.strftime('%H:%M') if self.end_time else None,
            'status': self.status,
            'session_type': self.session_type,
            'specialization': self.specialization,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'rejected_at': self.rejected_at.isoformat() if self.rejected_at else None,
            'cancelled_at': self.cancelled_at.isoformat() if self.cancelled_at else None,
            'coach_name': self.coach.name if self.coach else None,
            'client_name': self.client.name if self.client else None
        }