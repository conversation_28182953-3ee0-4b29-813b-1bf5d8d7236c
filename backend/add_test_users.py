#!/usr/bin/env python3
"""
Add test users to the database
"""

import sys
import os
sys.path.append('.')

from app import create_app
from models import db, User, Admin
from datetime import datetime

def add_test_users():
    """Add test users to the database"""
    
    app = create_app()
    
    with app.app_context():
        print("🚀 Adding test users...")
        
        # Ensure admin exists
        existing_admin = Admin.query.filter_by(email='<EMAIL>').first()
        if not existing_admin:
            admin = Admin(
                name='System Admin',
                email='<EMAIL>'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("✅ Admin user created")
        else:
            print("ℹ️  Admin user already exists")
        
        # Test users to add
        test_users = [
            {
                'name': '<PERSON>',
                'email': '<EMAIL>',
                'password': 'password123',
                'phone': '******-567-8900',
                'credential': 'ACC In Progress',
                'institution': 'Coaching Academy',
                'country': 'USA'
            },
            {
                'name': '<PERSON><PERSON><PERSON>',
                'email': '<EMAIL>',
                'password': 'vivek123',
                'phone': '+91-9876543210',
                'credential': 'PCC Certified',
                'institution': 'International Coach Federation',
                'country': 'India'
            },
            {
                'name': 'Sarah Johnson',
                'email': '<EMAIL>',
                'password': 'sarah123',
                'phone': '******-123-4567',
                'credential': 'MCC Certified',
                'institution': 'Global Coaching Institute',
                'country': 'Canada'
            }
        ]
        
        for user_data in test_users:
            try:
                existing_user = User.query.filter_by(email=user_data['email']).first()
                if not existing_user:
                    user = User(
                        name=user_data['name'],
                        email=user_data['email'],
                        phone=user_data['phone'],
                        credential=user_data['credential'],
                        institution=user_data['institution'],
                        country=user_data['country'],
                        status='approved',
                        approved_at=datetime.utcnow()
                    )
                    user.set_password(user_data['password'])
                    db.session.add(user)
                    db.session.commit()
                    print(f"✅ User created: {user_data['name']} ({user_data['email']})")
                else:
                    print(f"ℹ️  User already exists: {user_data['name']}")
            except Exception as e:
                print(f"⚠️  Error creating user {user_data['name']}: {e}")
        
        # Show all users
        print("\n📋 All users in database:")
        all_users = User.query.all()
        for user in all_users:
            print(f"  - {user.name} ({user.email}) - Status: {user.status}")
        
        print(f"\n✅ Database setup completed! Total users: {len(all_users)}")

if __name__ == '__main__':
    add_test_users()
