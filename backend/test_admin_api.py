#!/usr/bin/env python3
"""
Test admin API endpoints to verify they work
"""

import requests
import json

API_BASE_URL = 'http://127.0.0.1:8000'

def test_admin_endpoints():
    """Test admin API endpoints"""
    
    print("🧪 Testing Admin API Endpoints...")
    print(f"API Base URL: {API_BASE_URL}")
    
    # Test 1: Check if server is running
    try:
        response = requests.get(f'{API_BASE_URL}/')
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print(f"❌ Server not responding: {response.status_code}")
            return
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure backend is running on port 8000")
        return
    
    # Test 2: Admin login
    print("\n🔐 Testing admin login...")
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    session = requests.Session()
    response = session.post(f'{API_BASE_URL}/api/admin/login', json=login_data)
    
    if response.status_code == 200:
        print("✅ Admin login successful")
        admin_data = response.json()
        print(f"   Admin: {admin_data.get('admin', {}).get('name', 'Unknown')}")
    else:
        print(f"❌ Admin login failed: {response.status_code}")
        print(f"   Response: {response.text}")
        return
    
    # Test 3: Get approved users
    print("\n👥 Testing get approved users...")
    response = session.get(f'{API_BASE_URL}/api/admin/approved-users')
    
    if response.status_code == 200:
        data = response.json()
        users = data.get('approved_users', [])
        print(f"✅ Successfully fetched {len(users)} approved users")
        
        for user in users:
            print(f"   - {user.get('name', 'Unknown')} ({user.get('email', 'Unknown')})")
            print(f"     Status: {user.get('status', 'Unknown')}")
            print(f"     Last Login: {user.get('last_login', 'Never')}")
    else:
        print(f"❌ Failed to fetch approved users: {response.status_code}")
        print(f"   Response: {response.text}")
    
    # Test 4: Get pending users
    print("\n⏳ Testing get pending users...")
    response = session.get(f'{API_BASE_URL}/api/admin/pending-users')
    
    if response.status_code == 200:
        data = response.json()
        users = data.get('pending_users', [])
        print(f"✅ Successfully fetched {len(users)} pending users")
        
        for user in users:
            print(f"   - {user.get('name', 'Unknown')} ({user.get('email', 'Unknown')})")
    else:
        print(f"❌ Failed to fetch pending users: {response.status_code}")
        print(f"   Response: {response.text}")
    
    print("\n🎯 Test Summary:")
    print("If all tests passed, the admin panel should work correctly.")
    print("\n🔑 Test Credentials:")
    print("   Admin: <EMAIL> / admin123")
    print("   User 1: <EMAIL> / password123")
    print("   User 2: <EMAIL> / vivek123")

if __name__ == '__main__':
    test_admin_endpoints()
