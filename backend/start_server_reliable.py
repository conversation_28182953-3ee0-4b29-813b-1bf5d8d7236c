#!/usr/bin/env python3
"""
Reliable startup script for CoachCentral
"""

import os
import sys
from datetime import datetime

# Ensure we're in the right directory
os.chdir(os.path.dirname(os.path.abspath(__file__)))

# Add to Python path
sys.path.insert(0, '.')

def start_server():
    """Start the server with proper initialization"""
    try:
        from app import create_app
        from models import db
        
        print("🚀 Starting CoachCentral Server...")
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        app = create_app()
        
        # Verify database connection
        with app.app_context():
            try:
                # Test database connection
                from models import Admin, User
                admin_count = Admin.query.count()
                user_count = User.query.count()
                
                print(f"📊 Database connected: {admin_count} admins, {user_count} users")
                
            except Exception as e:
                print(f"⚠️  Database issue: {e}")
                print("🔧 Run: python3 permanent_fix.py to fix")
                return
        
        print("\n🔑 Login Credentials:")
        print("   Admin: <EMAIL> / admin123")
        print("   User 1: <EMAIL> / password123")
        print("   User 2: <EMAIL> / vivek123")
        print("   User 3: <EMAIL> / sarah123")
        print("   Pending: <EMAIL> / pending123")
        print("\n🌐 Server URL: http://127.0.0.1:8000")
        print("=" * 50)
        
        # Start server
        app.run(debug=True, host='127.0.0.1', port=8000)
        
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    start_server()
