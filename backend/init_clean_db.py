#!/usr/bin/env python3
"""
Clean Database Initialization Script
This is the ONLY database initialization script you should use.
It creates a clean, unified database with proper test data.
"""

from app import create_app
from models import db, Admin, User
from datetime import datetime

def init_clean_database():
    """Initialize a clean database with essential test data"""
    print("🚀 Initializing Clean CoachCentral Database")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        # Create all tables
        print("📋 Creating database tables...")
        db.create_all()
        print("✅ Database tables created")
        
        # Create admin user
        print("👤 Creating admin user...")
        existing_admin = Admin.query.filter_by(email='<EMAIL>').first()
        if not existing_admin:
            admin = Admin(
                name='System Admin',
                email='<EMAIL>'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            print("✅ Admin user created")
        else:
            print("ℹ️  Admin user already exists")
        
        # Create test approved user (coach)
        print("🏃‍♂️ Creating test coach...")
        existing_coach = User.query.filter_by(email='<EMAIL>').first()
        if not existing_coach:
            coach = User(
                name='John Coach',
                email='<EMAIL>',
                phone='+1234567890',
                credential='PCC Certified',
                institution='Coaching Institute',
                country='USA',
                status='approved'
            )
            coach.set_password('password123')
            coach.approve()
            db.session.add(coach)
            print("✅ Test coach created")
        else:
            print("ℹ️  Test coach already exists")
        
        # Create test approved user (client)
        print("👥 Creating test client...")
        existing_client = User.query.filter_by(email='<EMAIL>').first()
        if not existing_client:
            client = User(
                name='Jane Client',
                email='<EMAIL>',
                phone='+1234567891',
                credential='No Credential',
                institution='N/A',
                country='Canada',
                status='approved'
            )
            client.set_password('password123')
            client.approve()
            db.session.add(client)
            print("✅ Test client created")
        else:
            print("ℹ️  Test client already exists")
        
        # Create test pending user
        print("⏳ Creating test pending user...")
        existing_pending = User.query.filter_by(email='<EMAIL>').first()
        if not existing_pending:
            pending_user = User(
                name='Bob Pending',
                email='<EMAIL>',
                phone='+1234567892',
                credential='ACC In Progress',
                institution='Training Academy',
                country='UK',
                status='pending'
            )
            pending_user.set_password('password123')
            db.session.add(pending_user)
            print("✅ Test pending user created")
        else:
            print("ℹ️  Test pending user already exists")
        
        # Commit all changes
        db.session.commit()
        
        # Display summary
        admin_count = Admin.query.count()
        user_count = User.query.count()
        approved_count = User.query.filter_by(status='approved').count()
        pending_count = User.query.filter_by(status='pending').count()
        
        print("\n📊 Database Summary:")
        print(f"   Admins: {admin_count}")
        print(f"   Total Users: {user_count}")
        print(f"   Approved Users: {approved_count}")
        print(f"   Pending Users: {pending_count}")
        
        print("\n🎉 Clean database initialization complete!")
        print("=" * 50)
        print("\n🔑 Login Credentials:")
        print("📧 Admin Login:")
        print("   Email: <EMAIL>")
        print("   Password: admin123")
        print("\n👤 Test Coach Login:")
        print("   Email: <EMAIL>")
        print("   Password: password123")
        print("\n👥 Test Client Login:")
        print("   Email: <EMAIL>")
        print("   Password: password123")
        print("\n⏳ Test Pending User (needs admin approval):")
        print("   Email: <EMAIL>")
        print("   Password: password123")

if __name__ == '__main__':
    init_clean_database()
