#!/usr/bin/env python3
"""
Final test to verify everything is working
"""

import sys
import os
sys.path.append('.')

def test_everything():
    """Test that everything is working"""
    print("🧪 Final Test - Verifying Everything Works")
    print("=" * 50)
    
    try:
        # Test 1: Import app
        from app import create_app
        from models import db, Admin, User
        print("✅ App imports successful")
        
        # Test 2: Create app
        app = create_app()
        print("✅ App creation successful")
        
        # Test 3: Test database
        with app.app_context():
            # Create tables
            db.create_all()
            print("✅ Database tables created")
            
            # Test queries
            admin_count = Admin.query.count()
            user_count = User.query.count()
            approved_count = User.query.filter_by(status='approved').count()
            pending_count = User.query.filter_by(status='pending').count()
            
            print(f"📊 Database stats:")
            print(f"   - Admins: {admin_count}")
            print(f"   - Total users: {user_count}")
            print(f"   - Approved users: {approved_count}")
            print(f"   - Pending users: {pending_count}")
            
            # Test admin endpoints
            with app.test_client() as client:
                # Test admin login
                login_response = client.post('/api/admin/login', 
                    json={'email': '<EMAIL>', 'password': 'admin123'})
                
                if login_response.status_code == 200:
                    print("✅ Admin login test passed")
                    
                    # Test get approved users
                    users_response = client.get('/api/admin/approved-users')
                    if users_response.status_code == 200:
                        users_data = users_response.get_json()
                        approved_users = users_data.get('approved_users', [])
                        print(f"✅ Get approved users test passed ({len(approved_users)} users)")
                    else:
                        print(f"❌ Get approved users failed: {users_response.status_code}")
                        return False
                    
                    # Test get pending users
                    pending_response = client.get('/api/admin/pending-users')
                    if pending_response.status_code == 200:
                        pending_data = pending_response.get_json()
                        pending_users = pending_data.get('pending_users', [])
                        print(f"✅ Get pending users test passed ({len(pending_users)} users)")
                    else:
                        print(f"❌ Get pending users failed: {pending_response.status_code}")
                        return False
                        
                else:
                    print(f"❌ Admin login test failed: {login_response.status_code}")
                    return False
        
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Database schema is correct")
        print("✅ Admin endpoints work")
        print("✅ No compatibility issues")
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_everything()
    
    if success:
        print("\n🚀 READY TO START SERVER!")
        print("Run: python3 run_server.py")
        print("\n🔑 Login Credentials:")
        print("   Admin: <EMAIL> / admin123")
        print("   User 1: <EMAIL> / password123")
        print("   User 2: <EMAIL> / vivek123")
        print("   User 3: <EMAIL> / sarah123")
        print("   Pending: <EMAIL> / pending123")
    else:
        print("\n❌ TESTS FAILED - Need to debug")
        sys.exit(1)
