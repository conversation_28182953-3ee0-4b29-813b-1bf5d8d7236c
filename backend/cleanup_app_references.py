#!/usr/bin/env python3
"""
<PERSON><PERSON>t to clean up all ApprovedUser and PendingUser references in app.py
This will systematically remove all legacy model references and update the code
to use only the unified User model.
"""

import re

def clean_app_file():
    """Clean up app.py to remove all legacy model references"""
    
    # Read the current app.py file
    with open('app.py', 'r') as f:
        content = f.read()
    
    print("🧹 Cleaning up app.py...")
    
    # Remove ApprovedUser and PendingUser imports (already done)
    
    # Replace authentication checks
    old_auth_pattern = r'isinstance\(current_user, ApprovedUser\) or \(isinstance\(current_user, User\) and current_user\.status == \'approved\'\)'
    new_auth_pattern = r'isinstance(current_user, User) and current_user.status == \'approved\''
    content = re.sub(old_auth_pattern, new_auth_pattern, content)
    
    # Remove ApprovedUser queries and fallbacks
    patterns_to_remove = [
        # Remove ApprovedUser query fallbacks
        r'\s*# Fallback to legacy ApprovedUser.*?\n.*?ApprovedUser\.query\.get.*?\n',
        r'\s*# Check legacy ApprovedUser model.*?\n.*?ApprovedUser\.query\.get.*?\n.*?if approved_user:.*?\n.*?authenticated_user = approved_user.*?\n.*?print.*?ApprovedUser.*?\n',
        r'\s*# Check ApprovedUser model.*?\n.*?recent_approved = ApprovedUser\.query\.filter.*?\n.*?ApprovedUser\.last_login >= recent_time.*?\n.*?\.all\(\).*?\n.*?for approved_user in recent_approved:.*?\n.*?authenticated_user = approved_user.*?\n.*?print.*?ApprovedUser.*?\n.*?break.*?\n',
        r'\s*legacy_users = ApprovedUser\.query\.all\(\).*?\n',
        r'\s*coaches\.extend\(legacy_users\).*?\n',
        r'\s*if not coach:.*?\n.*?coach = ApprovedUser\.query\.get\(coach_id\).*?\n',
        r'\s*if not user:.*?\n.*?user = ApprovedUser\.query\.get\(user_id\).*?\n',
        r'\s*# Get all coaches \(both User and ApprovedUser\).*?\n',
        r'\s*# Check both ID and email to handle cases where user exists in both User and ApprovedUser tables.*?\n',
    ]
    
    for pattern in patterns_to_remove:
        content = re.sub(pattern, '', content, flags=re.MULTILINE | re.DOTALL)
    
    # Remove specific ApprovedUser references
    specific_removals = [
        r'elif user_type == \'ApprovedUser\':\s*user = ApprovedUser\.query\.get\(user_id\)\s*if user:\s*print\(f"✅ Authenticated via token \(ApprovedUser\): \{user\.name\}"\)\s*return user',
        r'recent_approved_users = ApprovedUser\.query\.filter\(\s*ApprovedUser\.last_login >= recent_time\s*\)\.all\(\)',
        r'print\(f"🔍 Found \{len\(recent_approved_users\)\} recent users in ApprovedUser model"\)',
        r'for user in recent_approved_users:\s*print\(f"✅ Authenticated via token fallback \(ApprovedUser\): \{user\.name\}"\)\s*return user',
        r'approved_user = ApprovedUser\.query\.filter_by\(resume_filename=filename\)\.first\(\)',
        r'if not \(pending_user or approved_user\):',
        r'legacy_users = ApprovedUser\.query\.all\(\)',
        r'all_users = users \+ legacy_users',
        r'\'type\': \'ApprovedUser\',',
        r'fallback_user = ApprovedUser\.query\.get\(fallback_user_id\)',
        r'# Get from legacy ApprovedUser model only if not already in new model',
        r'for user in legacy_users:\s*if user\.email not in seen_emails:',
    ]
    
    for pattern in specific_removals:
        content = re.sub(pattern, '', content, flags=re.MULTILINE | re.DOTALL)
    
    # Clean up empty lines and fix formatting
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)  # Remove multiple empty lines
    content = re.sub(r'\n\s*\n\s*#', '\n\n    #', content)  # Fix comment spacing
    
    # Write the cleaned content back
    with open('app.py', 'w') as f:
        f.write(content)
    
    print("✅ app.py cleaned up successfully!")

def clean_specific_functions():
    """Clean up specific functions that need manual attention"""
    
    with open('app.py', 'r') as f:
        lines = f.readlines()
    
    # Find and clean specific problematic sections
    cleaned_lines = []
    skip_lines = 0
    
    for i, line in enumerate(lines):
        if skip_lines > 0:
            skip_lines -= 1
            continue
            
        # Skip ApprovedUser related blocks
        if 'ApprovedUser' in line and ('query' in line or 'isinstance' in line):
            # Skip this line and potentially following related lines
            continue
        
        # Clean up authentication checks
        if 'isinstance(current_user, ApprovedUser)' in line:
            line = line.replace('isinstance(current_user, ApprovedUser) or (isinstance(current_user, User) and current_user.status == \'approved\')', 
                              'isinstance(current_user, User) and current_user.status == \'approved\'')
        
        cleaned_lines.append(line)
    
    # Write back the cleaned lines
    with open('app.py', 'w') as f:
        f.writelines(cleaned_lines)
    
    print("✅ Specific function cleanup completed!")

if __name__ == '__main__':
    print("🚀 Starting app.py cleanup...")
    clean_app_file()
    clean_specific_functions()
    print("🎉 app.py cleanup completed!")
