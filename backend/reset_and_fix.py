#!/usr/bin/env python3
"""
Complete database reset and fix
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add current directory to path
sys.path.append('.')

def reset_database():
    """Reset and recreate database with correct schema"""
    
    print("🔄 Resetting database...")
    
    # Remove existing database files
    db_files = ['instance/coachcentral.db', 'coachcentral.db', 'coaching_app.db']
    for db_file in db_files:
        if os.path.exists(db_file):
            os.remove(db_file)
            print(f"🗑️  Removed {db_file}")
    
    # Ensure instance directory exists
    os.makedirs('instance', exist_ok=True)
    
    # Create new database with correct schema
    db_path = 'instance/coachcentral.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("📋 Creating tables...")
    
    # Create admins table
    cursor.execute('''
        CREATE TABLE admins (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(120) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create users table with last_login column
    cursor.execute('''
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(120) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            credential VARCHAR(50) NOT NULL,
            institution VARCHAR(200) NOT NULL,
            country VARCHAR(100) NOT NULL,
            resume_filename VARCHAR(255),
            status VARCHAR(20) NOT NULL DEFAULT 'pending',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            approved_at DATETIME,
            declined_at DATETIME,
            last_login DATETIME
        )
    ''')
    
    # Create other tables
    cursor.execute('''
        CREATE TABLE pending_users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(120) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            credential VARCHAR(50) NOT NULL,
            institution VARCHAR(200) NOT NULL,
            country VARCHAR(100) NOT NULL,
            resume_filename VARCHAR(255),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE approved_users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(120) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            credential VARCHAR(50) NOT NULL,
            institution VARCHAR(200) NOT NULL,
            country VARCHAR(100) NOT NULL,
            resume_filename VARCHAR(255),
            approved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME NOT NULL,
            last_login DATETIME
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE availability_slots (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            available_time_from DATETIME NOT NULL,
            available_time_to DATETIME NOT NULL,
            status VARCHAR(20) DEFAULT 'available',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE bookings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            coach_id INTEGER NOT NULL,
            booking_time_from DATETIME NOT NULL,
            booking_time_to DATETIME NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            approved_at DATETIME,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (coach_id) REFERENCES users (id)
        )
    ''')
    
    print("✅ Tables created successfully")
    
    # Add admin user
    import bcrypt
    admin_password = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    cursor.execute('''
        INSERT INTO admins (name, email, password_hash)
        VALUES (?, ?, ?)
    ''', ('System Admin', '<EMAIL>', admin_password))
    
    # Add test users
    test_users = [
        ('John Doe', '<EMAIL>', 'password123', '******-567-8900', 'ACC In Progress', 'Coaching Academy', 'USA'),
        ('Vivek Agarwal', '<EMAIL>', 'vivek123', '+91-9876543210', 'PCC Certified', 'International Coach Federation', 'India'),
        ('Sarah Johnson', '<EMAIL>', 'sarah123', '******-123-4567', 'MCC Certified', 'Global Coaching Institute', 'Canada')
    ]
    
    for user_data in test_users:
        name, email, password, phone, credential, institution, country = user_data
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        cursor.execute('''
            INSERT INTO users (name, email, password_hash, phone, credential, institution, country, status, approved_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, 'approved', ?)
        ''', (name, email, password_hash, phone, credential, institution, country, datetime.utcnow()))
    
    conn.commit()
    conn.close()
    
    print("✅ Database reset completed!")
    print("👥 Added 1 admin and 3 approved users")
    
    return True

if __name__ == '__main__':
    print("🚀 Starting complete database reset...")
    success = reset_database()
    
    if success:
        print("\n🎉 Database reset completed successfully!")
        print("\n🔑 Test Credentials:")
        print("   Admin: <EMAIL> / admin123")
        print("   User 1: <EMAIL> / password123")
        print("   User 2: <EMAIL> / vivek123")
        print("   User 3: <EMAIL> / sarah123")
        print("\n📋 Next steps:")
        print("1. Stop your backend server (Ctrl+C)")
        print("2. Restart it: python3 run_server.py")
        print("3. Try the admin panel again")
    else:
        print("\n❌ Database reset failed!")
