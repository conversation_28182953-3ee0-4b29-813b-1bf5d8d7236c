#!/usr/bin/env python3
"""
Fix database schema immediately - add last_login column
"""

import sqlite3
import os
import sys

def fix_database_schema():
    """Fix the database schema by adding missing last_login column"""
    
    # Check all possible database locations
    possible_db_paths = [
        'instance/coachcentral.db',
        'coachcentral.db',
        'coaching_app.db',
        '../coachcentral.db'
    ]
    
    db_path = None
    for path in possible_db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ No database file found!")
        return False
    
    print(f"🔧 Working with database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current users table structure
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"📋 Current users table columns: {column_names}")
        
        # Add last_login column if it doesn't exist
        if 'last_login' not in column_names:
            print("➕ Adding last_login column to users table...")
            cursor.execute("ALTER TABLE users ADD COLUMN last_login DATETIME")
            print("✅ Added last_login column to users table")
        else:
            print("ℹ️  last_login column already exists in users table")
        
        # Check approved_users table if it exists
        try:
            cursor.execute("PRAGMA table_info(approved_users)")
            approved_columns = cursor.fetchall()
            approved_column_names = [col[1] for col in approved_columns]
            
            if 'last_login' not in approved_column_names:
                print("➕ Adding last_login column to approved_users table...")
                cursor.execute("ALTER TABLE approved_users ADD COLUMN last_login DATETIME")
                print("✅ Added last_login column to approved_users table")
            else:
                print("ℹ️  last_login column already exists in approved_users table")
        except sqlite3.OperationalError:
            print("ℹ️  approved_users table doesn't exist")
        
        # Commit changes
        conn.commit()
        
        # Verify the fix
        cursor.execute("PRAGMA table_info(users)")
        new_columns = cursor.fetchall()
        new_column_names = [col[1] for col in new_columns]
        
        print(f"✅ Updated users table columns: {new_column_names}")
        
        # Test a simple query
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"📊 Total users in database: {user_count}")
        
        # Show some users
        cursor.execute("SELECT id, name, email, status FROM users LIMIT 5")
        users = cursor.fetchall()
        print("👥 Sample users:")
        for user in users:
            print(f"  - ID: {user[0]}, Name: {user[1]}, Email: {user[2]}, Status: {user[3]}")
        
        conn.close()
        
        print("\n🎉 Database schema fix completed successfully!")
        print("🔄 Please restart your backend server now.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing database: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 Starting database schema fix...")
    success = fix_database_schema()
    
    if success:
        print("\n✅ Schema fix completed!")
        print("📋 Next steps:")
        print("1. Stop your backend server (Ctrl+C)")
        print("2. Restart it: python3 run_server.py")
        print("3. Try the admin panel again")
    else:
        print("\n❌ Schema fix failed!")
